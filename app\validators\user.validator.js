const { body, constantUtils } = require('../validators/parent.validator');

exports.createUserValidationRule = () => {
  return [
    body('callingName')
      .isString()
      .notEmpty()
      .matches(global.constant.NAME_REGEX_PERSONNELS)
      .withMessage(constantUtils.INVALID_CALLINGNAME)
      .optional({ checkFalsy: false }),
    body('lastName')
      .isString()
      .notEmpty()
      .matches(global.constant.NAME_REGEX_PERSONNELS)
      .withMessage(constantUtils.INVALID_LASTNAME),
    body('email')
      .isString()
      .isEmail()
      .withMessage(constantUtils.INVALID_EMAIL)
      .notEmpty()
      .withMessage(constantUtils.EMPTY_EMAIL),
    body('contactNumber')
      .isObject()
      .withMessage(constantUtils.INVALID_CONTACT_NUMBER_FORMAT)
      .notEmpty()
      .withMessage(constantUtils.EMPTY_CONTACT_NUMBER)
      .bail()
      .custom(value => {
        const { number } = value;
        if (!number) {
          throw new Error(constantUtils.INVALID_CONTACT_NUMBER);
        }
        return true;
      })
      .bail(),
    body('nextOfKin')
      .isArray({ max: 3 })
      .withMessage(constantUtils.USER_MAXIMUM_KIN)
      .optional({ checkFalsy: false }),
    body('nextOfKin.[*].kinName').notEmpty().withMessage(constantUtils.USER_KIN_NAME_REQUIRED),
    body('nextOfKin.[*].relationship')
      .notEmpty()
      .withMessage(constantUtils.USER_KIN_RELATIONSHIP_REQUIRED),
    body('nextOfKin.[*].kinStreet').notEmpty().withMessage(constantUtils.USER_KIN_STREET_REQUIRED),
    body('nextOfKin.[*].kinCity').notEmpty().withMessage(constantUtils.USER_KIN_CITY_REQUIRED),
    body('nextOfKin.[*].kinCountry')
      .notEmpty()
      .withMessage(constantUtils.USER_KIN_COUNTRY_REQUIRED),
    body('nextOfKin.[*].kinZip').notEmpty().withMessage(constantUtils.USER_KIN_ZIPCODE_REQUIRED),
    body('nextOfKin.[*].kinContactNumber')
      .exists({ checkNull: true, checkFalsy: true })
      .withMessage(constantUtils.EMPTY_CONTACT_NUMBER)
      .bail()
      .isObject()
      .withMessage(constantUtils.INVALID_CONTACT_NUMBER_FORMAT)
      .bail(),
    body('password')
      .isString()
      .notEmpty()
      .matches(constantUtils.PASSWORD_REGEX)
      .withMessage(constantUtils.INVALID_PASSWORD)
      .isLength({ min: global.constant.PASSWORD_MIN })
      .withMessage(constantUtils.PASSWORD_MIN_LENGTH),
    body('passport')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.USER_PASSPORT_NUMBER)
      .optional({ checkFalsy: false }),
  ];
};

exports.updateUserValidationRule = () => {
  return [
    body('callingName')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.INVALID_CALLINGNAME)
      .optional({ checkFalsy: false }),
    body('firstName')
      .notEmpty()
      .withMessage(constantUtils.INVALID_FIRSTNAME)
      .optional({ checkFalsy: false }),
    body('lastName')
      .notEmpty()
      .withMessage(constantUtils.INVALID_LASTNAME)
      .optional({ checkFalsy: false }),
    body('email')
      .notEmpty()
      .isEmail()
      .withMessage(constantUtils.INVALID_EMAIL)
      .optional({ checkFalsy: false }),
    body('travelTimeToAirport')
      .notEmpty()
      .isString()
      .withMessage(constantUtils.INVALID_TRAVEL_TIME_TO_AIRPORT)
      .optional({ checkFalsy: false }),
    body('travelTimeToSecondAirport')
      .notEmpty()
      .isString()
      .withMessage(constantUtils.INVALID_TRAVEL_TIME_TO_AIRPORT)
      .optional({ checkFalsy: false }),
    body('drivingLicence').custom((value, { req }) => {
      if (!req.body.drivingLicence) {
        return true; // If drivingLicence is not passed, skip further checks
      }

      if (!Array.isArray(value)) {
        throw new Error('drivingLicence must be an array');
      }

      // Validate each object in the array
      value.forEach((item, index) => {
        if (
          !item.licenseNumber ||
          typeof item.licenseNumber !== 'string' ||
          !item.licenseNumber.trim()
        ) {
          throw new Error(
            `drivingLicence[${index}].licenseNumber is required and must not be empty`
          );
        }
        if (!item.issueDate || typeof item.issueDate !== 'string' || !item.issueDate.trim()) {
          throw new Error(`drivingLicence[${index}].issueDate is required and must not be empty`);
        }
        if (!item.expiryDate || typeof item.expiryDate !== 'string' || !item.expiryDate.trim()) {
          throw new Error(`drivingLicence[${index}].expiryDate is required and must not be empty`);
        }
      });

      return true;
    }),
    body('seamansBook').custom((value, { req }) => {
      if (!req.body.seamansBook) {
        return true; // If seamansBook is not passed, skip further checks
      }

      if (!Array.isArray(value)) {
        throw new Error('seamansBook must be an array');
      }

      // Validate each object in the array
      value.forEach((item, index) => {
        if (!item.name || typeof item.name !== 'string' || !item.name.trim()) {
          throw new Error(`seamansBook[${index}].name is required and must not be empty`);
        }
        if (!item.url || typeof item.url !== 'string' || !item.url.trim()) {
          throw new Error(`seamansBook[${index}].url is required and must not be empty`);
        }
        if (!item.fromDate || typeof item.fromDate !== 'string' || !item.fromDate.trim()) {
          throw new Error(`seamansBook[${index}].fromDate is required and must not be empty`);
        }
        if (!item.toDate || typeof item.toDate !== 'string' || !item.toDate.trim()) {
          throw new Error(`seamansBook[${index}].toDate is required and must not be empty`);
        }
      });

      return true;
    }),
    body('curriculumVitae').custom((value, { req }) => {
      if (!req.body.curriculumVitae) {
        return true; // If curriculumVitae is not passed, skip further checks
      }

      if (!Array.isArray(value)) {
        throw new Error('curriculumVitae must be an array');
      }

      // Validate each object in the array
      value.forEach((item, index) => {
        if (!item.name || typeof item.name !== 'string' || !item.name.trim()) {
          throw new Error(`curriculumVitae[${index}].name is required and must not be empty`);
        }
        if (!item.url || typeof item.url !== 'string' || !item.url.trim()) {
          throw new Error(`curriculumVitae[${index}].url is required and must not be empty`);
        }
      });

      return true;
    }),
    body('nextOfKin.[*].kinName').notEmpty().withMessage(constantUtils.USER_KIN_NAME_REQUIRED),
    body('nextOfKin.[*].relationship')
      .notEmpty()
      .withMessage(constantUtils.USER_KIN_RELATIONSHIP_REQUIRED),
    body('nextOfKin.[*].kinStreet').notEmpty().withMessage(constantUtils.USER_KIN_STREET_REQUIRED),
    body('nextOfKin.[*].kinCity').notEmpty().withMessage(constantUtils.USER_KIN_CITY_REQUIRED),
    body('nextOfKin.[*].kinCountry')
      .notEmpty()
      .withMessage(constantUtils.USER_KIN_COUNTRY_REQUIRED),
    body('nextOfKin.[*].kinZip').notEmpty().withMessage(constantUtils.USER_KIN_ZIPCODE_REQUIRED),
    body('nextOfKin.[*].kinContactNumber')
      .exists({ checkNull: true, checkFalsy: true })
      .withMessage(constantUtils.EMPTY_CONTACT_NUMBER)
      .bail()
      .isObject()
      .withMessage(constantUtils.INVALID_CONTACT_NUMBER_FORMAT)
      .bail(),
    body('contactNumber')
      .isObject()
      .withMessage(constantUtils.INVALID_CONTACT_NUMBER_FORMAT)
      .notEmpty()
      .withMessage(constantUtils.EMPTY_CONTACT_NUMBER)
      .bail()
      .custom(value => {
        const { number } = value;
        if (!number) {
          throw new Error(constantUtils.INVALID_CONTACT_NUMBER);
        }
        return true;
      })
      .bail()
      .optional({ checkFalsy: false }),
  ];
};

exports.updateUserStatusValidationRule = () => {
  return [body('user').notEmpty().withMessage(constantUtils.NO_USER)];
};

exports.submitUserRatingValidationRule = () => {
  return [body('rating').isNumeric().notEmpty().withMessage(constantUtils.NO_RATING)];
};
