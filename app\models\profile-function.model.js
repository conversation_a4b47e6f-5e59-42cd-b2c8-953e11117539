const mongoose = require('mongoose');

const ProfileFunction = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    sortOrder: {
      type: Number,
      default: 0,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true, collation: { locale: 'en', strength: 2 } }
);

// Add indexes for better performance
ProfileFunction.index({ account: 1, deletedAt: 1 });
ProfileFunction.index({ account: 1, isActive: 1, deletedAt: 1 });
ProfileFunction.index({ sortOrder: 1 });

module.exports = mongoose.model('profile-function', ProfileFunction);
