require('dotenv').config();
const bcrypt = require('bcryptjs');
const randomId = require('rand-token').uid;

// services
const userService = require('../services/auth.service');
const userLogService = require('../services/userlog.service');
const roleService = require('../services/role.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const mailerUtils = require('../utils/mailer.utils');
const commonUtils = require('../utils/common.utils');

// Middleware
const authMiddleware = require('../middlewares/auth.middleware');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Users Login
 * Post Request ==> api/auth/login
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.login = async (req, res, next) => {
  try {
    let error = new Error();
    let { email, password, userAgent, location } = req.body;
    let isExists = await userService.getUserByEmail(email.toLowerCase());
    if (!isExists) {
      error.status = 400;
      error.message = constantUtils.USER_NOT_REGISTERED;
      throw error;
    }

    //Check the user is inactive
    if (!isExists.isActive) {
      error.status = HTTP_STATUS.BAD_REQUEST;
      error.message = constantUtils.RESTRICTED_EMAIL_UPDATE;
      throw error;
    }

    if (!isExists.role.isActive) {
      error.status = HTTP_STATUS.UNAUTHORIZED;
      error.message = constantUtils.ROLE_INACTIVETED;
      throw error;
    }

    let payload = {};
    if (
      Object.hasOwn(req.body, 'isMobile') &&
      !req.body.isMobile &&
      isExists.role.accessType == 'mobile'
    ) {
      error.status = 403;
      error.message = constantUtils.MOBILE_ONLY;
      throw error;
    }

    if (
      Object.hasOwn(req.body, 'isMobile') &&
      req.body.isMobile &&
      isExists.role.accessType == 'web'
    ) {
      error.status = 403;
      error.message = constantUtils.WEB_ONLY;
      throw error;
    }

    // Checking if password field is not exist in DB
    if (!isExists.password) {
      error.status = 400;
      error.message = constantUtils.NO_PASSWORD_FIELD_EXISTS;
      throw error;
    }

    // Using bcrypt library to compare hashPassword with incoming password
    let isMatch = bcrypt.compareSync(password, isExists.password);
    if (!isMatch) {
      error.status = 400;
      error.message = constantUtils.PASSWORD_INCORRECT;
      throw error;
    }

    let userRole = isExists.role;
    if (commonUtils.isValidId(userRole?._id)) {
      let { title } = await roleService.getRoleById(userRole?._id, isExists.account);
      userRole = title ?? userRole;
    }

    // Passing payload as userId to generate token.
    let token = await authMiddleware.assignToken(
      isExists,
      userRole,
      isExists.role,
      req.body.isMobile
    );
    // Get user detail By Id
    let user = await userService.getUserById(isExists._id);
    payload.token = token;
    payload.user = {
      email: user.email,
      fullName: user.firstName + ' ' + user.lastName,
      role: userRole,
    };

    let lastLogin = new Date().toISOString();
    let userId = isExists._id;
    const userLogExist = await userLogService.checkIfUserLogExist(userId);

    if (!userLogExist) {
      await userLogService.createUserLog({ userId, userAgent, location, lastLogin });
    } else {
      await userLogService.updateUserLogExist(userId, { userId, userAgent, location, lastLogin });
    }

    res.status(200).json(responseUtils.successResponse(constantUtils.USER_LOGIN, { payload }));
    next();
  } catch (err) {
    let statusErr = err.status ?? 500;
    res.status(statusErr).json(responseUtils.errorResponse(err.message));
    next();
  }
};

/**
 * Reset Password
 * Patch Request ==> api/auth/resetPassword/:id
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.resetPassword = async (req, res) => {
  try {
    let id = req.params.id;
    let { newPassword, confirmPassword } = req.body;
    let user = await userService.getUserById(id);

    if (user.resetToken === '') {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.PASSWORD_LINK_EXPIRED));
    }
    let expiryTime = new Date(user.resetExpiresIn).getTime();

    // Checking compare currentTime with resetExpiry.
    if (Date.now() > expiryTime) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.PASSWORD_LINK_EXPIRED));
    }
    if (newPassword !== confirmPassword) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.PASWORD_DOES_NOT_MATCH));
    }
    // Using bcrypt library to hashing confirmPassword.
    let hashPass = bcrypt.hashSync(confirmPassword);
    await userService.updatePasswordById(id, hashPass);
    await mailerUtils.sendMailer(req.body.email, process.env.SENDGRID_CREDIANTIAL_CHANGE_EMAIL, {
      email: user.email,
      password: newPassword,
      userName: `${user.firstName} ${user.lastName}`,
      supportTeamEmail: process.env.SUPPORT_TEAM_EMAIL,
      isAdmin: false,
      accessType: user.role.accessType,
      currentYear: new Date().getFullYear(),
      bestRegards: process.env.BEST_REGARDS,
      logo: global.constant.APP_LOGO,
    });
    res.status(200).json(responseUtils.successResponse(constantUtils.PASSWORD_RESET_SUCCESSFULLY));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Forgot Password
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.forgetPassword = async (req, res) => {
  try {
    let email = req.body.email?.toLowerCase();
    let isExists = await userService.getUserByEmail(email);

    if (!isExists) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.USER_NOT_REGISTERED));
    }
    //  Generating expiryTime that valid for 30 minutes After Mail Sent.
    let user = await userService.insertResetTimeAndToken(email, {
      expiryTime: new Date(Date.now() + parseInt(30 * 60 * 1000)),
      token: randomId(16),
    });
    const template = process.env.SENDGRID_FORGOT_PASSWORD_TEMPLATE;
    const data = {
      username: isExists.firstName + ' ' + isExists.lastName,
      bestRegards: process.env.BEST_REGARDS,
      supportTeamEmail: process.env.SUPPORT_TEAM_EMAIL,
      currentYear: new Date().getFullYear(),
      urlLink: `${process.env.BASE_URL}/authentication/reset-password/${user._id}/${user.resetToken}`,
      logo: global.constant.APP_LOGO,
    };

    await mailerUtils.sendMailer(email, template, data);

    res.status(200).json(responseUtils.successResponse(constantUtils.PASSWORD_RESET_LINK));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Logout
 * Get Request ==> /api/auth/logout
 *
 * @param {*} req
 * @param {*} res
 */
exports.logout = async (req, res) => {
  try {
    req.userData = null;
    res.status(200).json(responseUtils.successResponse(constantUtils.LOGOUT_SUCCESS));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * check the reset token is exist
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.checkResetToken = async (req, res) => {
  try {
    let resetToken = req.params.resetToken;
    const exist = await userService.getUserDataByFilter({ resetToken });
    if (!exist) {
      return res.status(401).json(responseUtils.errorResponse(constantUtils.RESET_TOKEN_EXPIRED));
    }
    res.status(200).json(responseUtils.successResponse(constantUtils.TOKEN_EXIST));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};
