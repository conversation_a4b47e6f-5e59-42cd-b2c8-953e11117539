const Member = require('../models/member.model');
const Project = require('../models/project.model');
require('dotenv').config();

/**
 * Create Member
 *
 * @param {*} member
 * @returns
 */
exports.createMember = async member => {
  return await Member.create(member);
};

/**
 * Get By filter
 *
 * @param {*} filter
 * @returns
 */
exports.getMember = async filter => {
  return await Member.findOne(filter);
};

exports.createMember = async member => {
  return await Member.create(member);
};

/**
 * Update By Id
 *
 * @param {*} id
 * @param {*} member
 * @returns
 */
exports.updateMember = async (id, member) => {
  return Member.findByIdAndUpdate(id, { $set: member }, { new: true }).populate([
    {
      path: 'user',
      select: { callingName: 1, firstName: 1, lastName: 1, _id: 1, email: 1 },
      strictPopulate: false,
    },
    {
      path: 'project',
      select: { title: 1, _id: 1, projectNumber: 1 },
      strictPopulate: false,
    },
    {
      path: 'account',
      select: { name: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'function',
      select: { functionName: 1, _id: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Delete By Id
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteMember = async (id, deletedAt) => {
  return Member.findByIdAndUpdate(id, { $set: deletedAt });
};

/**
 * Get All Member
 *
 * @returns
 */
exports.getAllMember = async filter => {
  return Member.find(filter, { __v: 0 })
    .select('_id')
    .populate([
      {
        path: 'user',
        select: { callingName: 1, firstName: 1, lastName: 1, _id: 1, email: 1, contactNumber: 1 },
        strictPopulate: false,
      },
      {
        path: 'project',
        select: { title: 1, _id: 1, projectNumber: 1 },
        strictPopulate: false,
      },
      {
        path: 'account',
        select: { name: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'function',
        select: { functionName: 1, _id: 1 },
        strictPopulate: false,
      },
    ])
    .lean();
};

/**
 * Get All Member - Optimized for project matrix
 *
 * @returns
 */
exports.getAllMemberOptimized = async filter => {
  return Member.find(filter, {
    _id: 1,
    user: 1,
    function: 1,
    rotation: 1,
    project: 1,
  })
    .populate([
      {
        path: 'user',
        select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'function',
        select: { functionName: 1, _id: 1 },
        strictPopulate: false,
      },
    ])
    .lean();
};

/**
 * Get Member By Id
 *
 * @param {*} id
 * @returns
 */
exports.getMemberById = async filter => {
  return Member.findOne(filter, { createdAt: 0, updatedAt: 0, __v: 0 }).populate([
    {
      path: 'user',
      select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'project',
      select: { title: 1, _id: 1, projectNumber: 1 },
      strictPopulate: false,
    },
    {
      path: 'account',
      select: { name: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'function',
      select: { functionName: 1, _id: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Get Member By Name
 *
 * @param {*} memberName
 * @returns
 */
exports.getMemberByFilter = async filter => {
  return Member.find(filter, { updatedAt: 0, updatedBy: 0, deletedAt: 0, deletedBy: 0, __v: 0 });
};

/**
 * Get All Member
 *
 * @returns
 */
exports.getAllAccountMember = async account => {
  return Member.find({ $and: [{ account: account }, { deletedAt: null }] }, { __v: 0 })
    .select('_id')
    .populate([
      {
        path: 'user',
        select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'project',
        select: { title: 1, _id: 1, projectNumber: 1 },
        strictPopulate: false,
      },
      {
        path: 'account',
        select: { name: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'function',
        select: { functionName: 1, _id: 1 },
        strictPopulate: false,
      },
    ]);
};

/**
 * Get Member By Name
 *
 * @param {*} memberName
 * @returns
 */
exports.getMemberByIds = async memberIds => {
  return Member.find({ _id: { $in: memberIds } });
};

/**
 * Delete All Project's Member
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteAllProjectMember = async (projectId, deletedAt) => {
  return Member.updateMany(
    { project: projectId },
    {
      $set: deletedAt,
    }
  );
};

/**
 * Get assign project list
 *
 * @param {*} filter
 * @returns
 */
exports.fetchAssignProjects = async filter => {
  const getProjects = await Member.find(filter, { __v: 0 });
  let projects = [];
  let getOtherProject = await Project.findOne({
    account: filter.account,
    standByTypes: process.env.DEFAULT_PROJECT,
  });
  if (getOtherProject) {
    projects.push(getOtherProject._id);
    if (getProjects.length > 0) {
      Object.keys(getProjects).forEach(key => {
        projects.push(getProjects[key].project);
      });
    }
  }
  return projects;
};

/**
 * Get member list by project id
 *
 * @param {*} filter
 * @param {*} field
 * @param {*} order
 * @param {*} page
 * @param {*} perPage
 */
exports.getMemberByProjectId = async (filter, field, order, page, perPage, name) => {
  let aggregate = [
    { $match: filter },
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'user',
      },
    },
    { $unwind: '$user' },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'project',
      },
    },
    { $unwind: { path: '$project', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        as: 'account',
      },
    },
    { $unwind: { path: '$account', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'functions',
        localField: 'function',
        foreignField: '_id',
        as: 'function',
      },
    },
    { $unwind: { path: '$function', preserveNullAndEmptyArrays: true } },
    {
      $addFields: {
        fullName: {
          $concat: [
            { $toLower: '$user.callingName' },
            ' ',
            { $toLower: '$user.firstName' },
            ' ',
            { $toLower: '$user.lastName' },
          ],
        },
      },
    },
  ];
  if (name) {
    aggregate.push({
      $match: {
        $expr: {
          $regexMatch: {
            input: '$fullName',
            regex: name,
            options: 'i',
          },
        },
      },
    });
  }
  if (field) {
    aggregate.push({
      $sort: {
        fullName: order,
      },
    });
  } else {
    aggregate.push({
      $sort: {
        createdAt: order,
      },
    });
  }
  if (page !== null && perPage !== null) {
    aggregate.push({ $skip: page * perPage }, { $limit: perPage });
  }

  aggregate.push({
    $project: {
      'user._id': 1,
      'user.callingName': 1,
      'user.firstName': 1,
      'user.lastName': 1,
      'user.email': 1,
      'project._id': 1,
      'project.title': 1,
      'account._id': 1,
      'account.name': 1,
      'function._id': 1,
      'function.functionName': 1,
      rotation: 1,
      isApprover: 1,
      showOnDpr: 1,
      deletedAt: 1,
      deletedBy: 1,
      createdAt: 1,
      updatedAt: 1,
    },
  });
  return await Member.aggregate(aggregate);
};

exports.getMemberByUserId = async filter => {
  let aggregate = [
    { $match: filter },
    {
      $group: {
        _id: '$user',
        projects: { $addToSet: '$project' },
      },
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'projects',
        foreignField: '_id',
        as: 'projectDetails',
        pipeline: [
          { $match: { status: 'open' } },
          {
            $project: {
              _id: 1,
              title: 1,
              projectNumber: 1,
            },
          },
        ],
      },
    },
    {
      $project: {
        projectDetails: 1,
      },
    },
  ];
  return await Member.aggregate(aggregate);
};
