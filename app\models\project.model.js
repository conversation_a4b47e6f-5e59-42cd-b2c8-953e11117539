const mongoose = require('mongoose');

const Project = new mongoose.Schema(
  {
    title: {
      type: String,
    },
    projectNumber: {
      type: String,
      default: '',
    },
    client: {
      type: String,
      default: '',
    },
    standByTypes: {
      type: String,
    },
    defaultIdentifier: {
      type: String,
      default: global.constant.NORMAL_DATA_IDENTIFIER, // DEFAULT_DATA_IDENTIFIER: for default project, NORMAL_DATA_IDENTIFIER: for normal project
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    isDeletable: {
      type: Boolean,
      default: true,
    },
    status: {
      type: String,
      enum: ['open', 'completed', 'closed'],
      default: 'open',
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

// Add indexes for better performance
Project.index({ account: 1, deletedAt: 1 });
Project.index({ account: 1, isActive: 1, deletedAt: 1 });
Project.index({ account: 1, status: 1, deletedAt: 1 });
Project.index({ account: 1, isDefault: 1, deletedAt: 1 });

module.exports = mongoose.model('project', Project);
